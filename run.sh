#!/bin/bash

# Configuration
MODEL_NAME="qwen2.5_7B"
DATA_NAME="/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl"
MAX_LENGTH=16384
RUN_NAME="LIMO_Qwen2.5_7B"

# Alternative data configuration (commented out)
# DATA_NAME="/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl"
# MAX_LENGTH=16384

# Training parameters
LEARNING_RATE=1e-5
EPOCHS=2

# Create logs directory if it doesn't exist
mkdir -p logs/train

echo "Starting training with the following configuration:"
echo "Model: $MODEL_NAME"
echo "Data: $DATA_NAME"
echo "Run name: $RUN_NAME"
echo "Learning rate: $LEARNING_RATE"
echo "Epochs: $EPOCHS"
echo "Log file: logs/train/${RUN_NAME}.log"
echo ""

# Create DeepSpeed config file
cat > ds_config_stage3.json << 'EOF'
{
    "zero_optimization": {
        "stage": 3,
        "offload_optimizer": {
            "device": "cpu",
            "pin_memory": true
        },
        "offload_param": {
            "device": "cpu",
            "pin_memory": true
        },
        "overlap_comm": true,
        "contiguous_gradients": true,
        "sub_group_size": 1e9,
        "reduce_bucket_size": "auto",
        "stage3_prefetch_bucket_size": "auto",
        "stage3_param_persistence_threshold": "auto",
        "stage3_max_live_parameters": 1e9,
        "stage3_max_reuse_distance": 1e9,
        "stage3_gather_16bit_weights_on_model_save": true
    },
    "fp16": {
        "enabled": false
    },
    "bf16": {
        "enabled": true
    },
    "optimizer": {
        "type": "AdamW",
        "params": {
            "lr": "auto",
            "betas": "auto",
            "eps": "auto",
            "weight_decay": "auto"
        }
    },
    "scheduler": {
        "type": "WarmupLR",
        "params": {
            "warmup_min_lr": "auto",
            "warmup_max_lr": "auto",
            "warmup_num_steps": "auto"
        }
    },
    "zero_allow_untested_optimizer": true,
    "gradient_accumulation_steps": "auto",
    "gradient_clipping": "auto",
    "steps_per_print": 1,
    "train_batch_size": "auto",
    "train_micro_batch_size_per_gpu": "auto",
    "wall_clock_breakdown": false
}
EOF

# Run training in background with nohup
nohup bash -c "
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 deepspeed --num_gpus=8 --master_port=49056 train.py \
--deepspeed ds_config_stage3.json \
--proctitle junkim100 \
--model_name_or_path $MODEL_NAME \
--data_name '$DATA_NAME' \
--wb_project \"kullm-pro\" \
--wb_name $RUN_NAME \
--output_name $RUN_NAME \
--max_length $MAX_LENGTH \
--num_train_epochs $EPOCHS \
--per_device_train_batch_size 1 \
--per_device_eval_batch_size 1 \
--gradient_accumulation_steps 1 \
--save_only_model \
--learning_rate $LEARNING_RATE \
--weight_decay 0. \
--warmup_ratio 0. \
--lr_scheduler_type cosine \
--bf16 True \
--tf32 True \
--gradient_checkpointing True \
--logging_steps 1
" &> logs/train/${RUN_NAME}.log &

# Get the process ID
PID=$!

echo "Training started in background with PID: $PID"
echo "To monitor progress, run: tail -f logs/train/${RUN_NAME}.log"
echo "To check if process is running: ps -p $PID"
echo "To kill the process: kill $PID"
