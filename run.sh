#!/bin/bash

# Configuration
MODEL_NAME="qwen2.5_7B"
DATA_NAME="/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl"
MAX_LENGTH=16384
RUN_NAME="LIMO_Qwen2.5_7B"

# Alternative data configuration (commented out)
# DATA_NAME="/data_x/junkim100/projects/KULLM/finetune/data/code_switched_GAIR_LIMO_train_817.jsonl"
# MAX_LENGTH=16384

# Training parameters
LEARNING_RATE=1e-5
EPOCHS=2

# Create logs directory if it doesn't exist
mkdir -p logs/train

echo "Starting training with the following configuration:"
echo "Model: $MODEL_NAME"
echo "Data: $DATA_NAME"
echo "Run name: $RUN_NAME"
echo "Learning rate: $LEARNING_RATE"
echo "Epochs: $EPOCHS"
echo "Log file: logs/train/${RUN_NAME}.log"
echo ""

# Run training in background with nohup
nohup bash -c "
CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7 deepspeed --num_gpus=8 --master_port=49056 train.py \
--proctitle junkim100 \
--model_name_or_path $MODEL_NAME \
--data_name '$DATA_NAME' \
--wb_project \"kullm-pro\" \
--wb_name $RUN_NAME \
--output_name $RUN_NAME \
--max_length $MAX_LENGTH \
--num_train_epochs $EPOCHS \
--per_device_train_batch_size 1 \
--per_device_eval_batch_size 1 \
--gradient_accumulation_steps 1 \
--save_only_model \
--learning_rate $LEARNING_RATE \
--weight_decay 0. \
--warmup_ratio 0. \
--lr_scheduler_type cosine \
--bf16 True \
--logging_steps 1 \
--fsdp \"full_shard auto_wrap\" \
--tf32 True
" &> logs/train/${RUN_NAME}.log &

# Get the process ID
PID=$!

echo "Training started in background with PID: $PID"
echo "To monitor progress, run: tail -f logs/train/${RUN_NAME}.log"
echo "To check if process is running: ps -p $PID"
echo "To kill the process: kill $PID"
