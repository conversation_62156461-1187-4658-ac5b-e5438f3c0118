[2025-07-04 06:17:32,282] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 06:17:34,960] [WARNING] [runner.py:215:fetch_hostfile] Unable to find hostfile, will proceed with training with local resources only.
Detected VISIBLE_DEVICES=0,1,2,3,4,5,6,7 but ignoring it because one or several of --include/--exclude/--num_gpus/--num_nodes cl args were used. If you want to use CUDA_VISIBLE_DEVICES don't pass any of these arguments to deepspeed.
[2025-07-04 06:17:34,960] [INFO] [runner.py:605:main] cmd = /mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10 -u -m deepspeed.launcher.launch --world_info=eyJsb2NhbGhvc3QiOiBbMCwgMSwgMiwgMywgNCwgNSwgNiwgN119 --master_addr=127.0.0.1 --master_port=49056 --enable_each_rank_log=None train.py --deepspeed deepspeed3.json --proctitle junkim100 --model_name_or_path qwen2.5_7B --data_name /data_x/junkim100/projects/finetune/data/LIMO/train.jsonl --wb_project kullm-pro --wb_name LIMO_Qwen2.5_7B --output_name LIMO_Qwen2.5_7B --max_length 16384 --num_train_epochs 2 --per_device_train_batch_size 1 --per_device_eval_batch_size 1 --gradient_accumulation_steps 1 --save_only_model --learning_rate 1e-5 --weight_decay 0. --warmup_ratio 0. --lr_scheduler_type cosine --bf16 True --tf32 True --gradient_checkpointing True --logging_steps 1
[2025-07-04 06:17:36,693] [INFO] [real_accelerator.py:254:get_accelerator] Setting ds_accelerator to cuda (auto detect)
[2025-07-04 06:17:39,349] [INFO] [launch.py:146:main] WORLD INFO DICT: {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]}
[2025-07-04 06:17:39,349] [INFO] [launch.py:152:main] nnodes=1, num_local_procs=8, node_rank=0
[2025-07-04 06:17:39,349] [INFO] [launch.py:163:main] global_rank_mapping=defaultdict(<class 'list'>, {'localhost': [0, 1, 2, 3, 4, 5, 6, 7]})
[2025-07-04 06:17:39,349] [INFO] [launch.py:164:main] dist_world_size=8
[2025-07-04 06:17:39,349] [INFO] [launch.py:168:main] Setting CUDA_VISIBLE_DEVICES=0,1,2,3,4,5,6,7
[2025-07-04 06:17:39,351] [INFO] [launch.py:256:main] process 3708224 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=0', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 06:17:39,352] [INFO] [launch.py:256:main] process 3708225 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=1', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 06:17:39,352] [INFO] [launch.py:256:main] process 3708226 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=2', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 06:17:39,353] [INFO] [launch.py:256:main] process 3708227 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=3', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 06:17:39,354] [INFO] [launch.py:256:main] process 3708228 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=4', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 06:17:39,354] [INFO] [launch.py:256:main] process 3708229 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=5', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 06:17:39,355] [INFO] [launch.py:256:main] process 3708230 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=6', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
[2025-07-04 06:17:39,356] [INFO] [launch.py:256:main] process 3708231 spawned with command: ['/mnt/raid6/junkim100/miniconda3/envs/finetune/bin/python3.10', '-u', 'train.py', '--local_rank=7', '--deepspeed', 'deepspeed3.json', '--proctitle', 'junkim100', '--model_name_or_path', 'qwen2.5_7B', '--data_name', '/data_x/junkim100/projects/finetune/data/LIMO/train.jsonl', '--wb_project', 'kullm-pro', '--wb_name', 'LIMO_Qwen2.5_7B', '--output_name', 'LIMO_Qwen2.5_7B', '--max_length', '16384', '--num_train_epochs', '2', '--per_device_train_batch_size', '1', '--per_device_eval_batch_size', '1', '--gradient_accumulation_steps', '1', '--save_only_model', '--learning_rate', '1e-5', '--weight_decay', '0.', '--warmup_ratio', '0.', '--lr_scheduler_type', 'cosine', '--bf16', 'True', '--tf32', 'True', '--gradient_checkpointing', 'True', '--logging_steps', '1']
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e    
return _bootstrap._gcd_import(name[level:], package, level)      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module

return _bootstrap._gcd_import(name[level:], package, level)  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import

  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
    from .loss.loss_utils import LOSS_MAPPING
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>
    from .loss.loss_utils import LOSS_MAPPING
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>
    from .loss.loss_utils import LOSS_MAPPING    
from .loss_d_fine import DFineForObjectDetectionLoss      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>

from .loss_d_fine import DFineForObjectDetectionLoss  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>
    from .loss_d_fine import DFineForObjectDetectionLoss    
    from .loss_for_object_detection import (  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>
from .loss_for_object_detection import (

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
    from .loss_for_object_detection import (
      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
from transformers.image_transforms import center_to_corners_format    
from transformers.image_transforms import center_to_corners_format  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>
    from transformers.image_transforms import center_to_corners_format
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>
        from .image_utils import (from .image_utils import (

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from .image_utils import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from torchvision.transforms import InterpolationMode    
from torchvision.transforms import InterpolationMode  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>
    
from torchvision.transforms import InterpolationMode  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>
    from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skip
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
        from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skipfrom torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skip

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
    def meta_nms(dets, scores, iou_threshold):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register
    def meta_nms(dets, scores, iou_threshold):    
def meta_nms(dets, scores, iou_threshold):  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register
    
use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
        use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):

      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake
RuntimeErroruse_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1): 
operator torchvision::nms does not exist  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake


The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):    
if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):    RuntimeError
module = self._get_module(self._class_to_module[name]): RuntimeError
operator torchvision::nms does not exist:   File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module

operator torchvision::nms does not exist
The above exception was the direct cause of the following exception:


Traceback (most recent call last):

The above exception was the direct cause of the following exception:

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    raise e    
module = self._get_module(self._class_to_module[name])      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module

module = self._get_module(self._class_to_module[name])  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e    
return importlib.import_module("." + module_name, self.__name__)      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module

raise e  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
    return importlib.import_module("." + module_name, self.__name__)
      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
    from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'PreTrainedModel'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments    
from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    module = self._get_module(self._class_to_module[name])    
raise ModuleNotFoundError(      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module

raise ModuleNotFoundError(ModuleNotFoundError
: ModuleNotFoundErrorCould not import module 'PreTrainedModel'. Are this object's requirements defined correctly?: 
Could not import module 'PreTrainedModel'. Are this object's requirements defined correctly?
The above exception was the direct cause of the following exception:


Traceback (most recent call last):

The above exception was the direct cause of the following exception:

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    raise e    
module = self._get_module(self._class_to_module[name])  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    
module = self._get_module(self._class_to_module[name])  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    return importlib.import_module("." + module_name, self.__name__)    
raise e      File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module

raise e  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
    return importlib.import_module("." + module_name, self.__name__)    
return importlib.import_module("." + module_name, self.__name__)  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
    return _bootstrap._gcd_import(name[level:], package, level)
      File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
    from .integrations import (  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked

  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>
    from .integrations import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    from .integrations import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    from transformers import Trainer
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
    ModuleNotFoundErrorraise ModuleNotFoundError(    : 
raise ModuleNotFoundError(Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?ModuleNotFoundError

: ModuleNotFoundError
The above exception was the direct cause of the following exception:

Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?: Traceback (most recent call last):

Could not import module 'Trainer'. Are this object's requirements defined correctly?  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>

The above exception was the direct cause of the following exception:


Traceback (most recent call last):
  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>
    from transformers import Trainer    
from transformers import Trainer  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__

  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(    
raise ModuleNotFoundError(ModuleNotFoundError
: ModuleNotFoundErrorCould not import module 'Trainer'. Are this object's requirements defined correctly?: 
Could not import module 'Trainer'. Are this object's requirements defined correctly?
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
    from .loss.loss_utils import LOSS_MAPPING
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>
    from .loss_d_fine import DFineForObjectDetectionLoss
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>
    from .loss_for_object_detection import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
    from transformers.image_transforms import center_to_corners_format
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>
    from .image_utils import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from torchvision.transforms import InterpolationMode
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>
    from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skip
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
    def meta_nms(dets, scores, iou_threshold):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register
    use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):
RuntimeError: operator torchvision::nms does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
    from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'PreTrainedModel'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
    from .integrations import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>
    from transformers import Trainer
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'Trainer'. Are this object's requirements defined correctly?
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
    from .loss.loss_utils import LOSS_MAPPING
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>
    from .loss_d_fine import DFineForObjectDetectionLoss
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>
    from .loss_for_object_detection import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
    from transformers.image_transforms import center_to_corners_format
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>
    from .image_utils import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from torchvision.transforms import InterpolationMode
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>
    from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skip
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
    def meta_nms(dets, scores, iou_threshold):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register
    use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):
RuntimeError: operator torchvision::nms does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
    from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'PreTrainedModel'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
    from .integrations import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>
    from transformers import Trainer
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'Trainer'. Are this object's requirements defined correctly?
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
    from .loss.loss_utils import LOSS_MAPPING
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>
    from .loss_d_fine import DFineForObjectDetectionLoss
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>
    from .loss_for_object_detection import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
    from transformers.image_transforms import center_to_corners_format
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>
    from .image_utils import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from torchvision.transforms import InterpolationMode
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>
    from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skip
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
    def meta_nms(dets, scores, iou_threshold):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register
    use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):
RuntimeError: operator torchvision::nms does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
    from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'PreTrainedModel'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
    from .integrations import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>
    from transformers import Trainer
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'Trainer'. Are this object's requirements defined correctly?
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
    from .loss.loss_utils import LOSS_MAPPING
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>
    from .loss_d_fine import DFineForObjectDetectionLoss
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>
    from .loss_for_object_detection import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
    from transformers.image_transforms import center_to_corners_format
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>
    from .image_utils import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from torchvision.transforms import InterpolationMode
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>
    from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skip
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
    def meta_nms(dets, scores, iou_threshold):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register
    use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):
RuntimeError: operator torchvision::nms does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
    from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'PreTrainedModel'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
    from .integrations import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>
    from transformers import Trainer
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'Trainer'. Are this object's requirements defined correctly?
Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/modeling_utils.py", line 73, in <module>
    from .loss.loss_utils import LOSS_MAPPING
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_utils.py", line 21, in <module>
    from .loss_d_fine import DFineForObjectDetectionLoss
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_d_fine.py", line 21, in <module>
    from .loss_for_object_detection import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/loss/loss_for_object_detection.py", line 32, in <module>
    from transformers.image_transforms import center_to_corners_format
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_transforms.py", line 21, in <module>
    from .image_utils import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/image_utils.py", line 59, in <module>
    from torchvision.transforms import InterpolationMode
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/__init__.py", line 10, in <module>
    from torchvision import _meta_registrations, datasets, io, models, ops, transforms, utils  # usort:skip
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torchvision/_meta_registrations.py", line 164, in <module>
    def meta_nms(dets, scores, iou_threshold):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 828, in register
    use_lib._register_fake(op_name, func, _stacklevel=stacklevel + 1)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/library.py", line 198, in _register_fake
    handle = entry.fake_impl.register(func_to_register, source)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/torch/_library/fake_impl.py", line 31, in register
    if torch._C._dispatch_has_kernel_for_dispatch_key(self.qualname, "Meta"):
RuntimeError: operator torchvision::nms does not exist

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/integrations/integration_utils.py", line 37, in <module>
    from .. import PreTrainedModel, TFPreTrainedModel, TrainingArguments
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'PreTrainedModel'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2045, in __getattr__
    module = self._get_module(self._class_to_module[name])
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2075, in _get_module
    raise e
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2073, in _get_module
    return importlib.import_module("." + module_name, self.__name__)
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/importlib/__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
  File "<frozen importlib._bootstrap>", line 1050, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1027, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1006, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 688, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 883, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/trainer.py", line 42, in <module>
    from .integrations import (
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'get_reporting_integration_callbacks'. Are this object's requirements defined correctly?

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "/data_x/junkim100/projects/finetune/train.py", line 27, in <module>
    from transformers import Trainer
  File "/mnt/raid6/junkim100/miniconda3/envs/finetune/lib/python3.10/site-packages/transformers/utils/import_utils.py", line 2048, in __getattr__
    raise ModuleNotFoundError(
ModuleNotFoundError: Could not import module 'Trainer'. Are this object's requirements defined correctly?
